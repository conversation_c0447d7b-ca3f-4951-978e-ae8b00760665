const moment = require('moment');
const sitefotosWorkorderService = require('./sitefotos-workorder-services.js')
const apiWorkorderService = require('./api-workorder-services.js')
const serviceChannel = require('./external/servicechannel.service')
const aspireServices = require('./external/aspire-services.js');
const corrigoproService = require('./external/corrigopro-service')
const smsOneService = require('./external/smsone-services')
const fmPilot = require('./external/fm-pilot.service')
const command7Services = require('./external/command7-service')
const caseFMSServices = require('./external/casefms-services')
const serviceChannelPinService = require('./external/service-channel-pin.service')
const emcorServices = require('./external/emcor.service')
const wizardServices = require('./external/wizard.service')
const { deepSearchObject,deepSearchObjects } = require('../utils/common')
const { v5: uuidv5 } = require('uuid');
const { createPresignedPost } =require("@aws-sdk/s3-presigned-post");
const { s3Client } = require ("../utils/s3.js");
const {saveForm, fixFormContractor} = require('./form.services');
const bus = require('../utils/eventbus');
const { awaitQuery, updateObj, insertObj, awaitSafeQuery } = require('../utils/db')
const { sendEmailTemplateSES } = require('../utils/email');

const changeAssignment = async(workorderId, contacts, vendorId) => {
    let workorder = await awaitQuery(`select swo_id, swo_system_id,swo_form_id, swo_form_template_id, swo_vendor_id from sitefotos_work_orders where swo_id = ?`, [workorderId])
    if(!workorder || !workorder.length) {
        throw new Error('No workorder found for given ID.')
    }
    let formId = null
    //what other systems require that?
    if(workorder[0].swo_system_id == 2 && workorder[0].swo_form_id == null) {
        formId = workorder[0].swo_form_template_id;
    } else {
        formId = workorder[0].swo_form_id;
    }


    let form = await awaitQuery(`select sf_id from sitefotos_forms where sf_id = ?`, [formId])
    if(!form || !form.length) {
        throw new Error('No form found for given workorder ID.')
    }

    let formData = {
        sf_form_contact: JSON.stringify(contacts),
        sf_updated: Math.floor(new Date().getTime() / 1000),
    }
    await updateObj('sitefotos_forms', formData, ['sf_id'], [form[0].sf_id])
    await fixFormContractor(form[0].sf_id)
    return;
    
    

}

const fiwoSubmit = async(formSubmissionID) => {
    try {
        const fiwoSystem = await awaitQuery('select swof_system_id from sitefotos_forms_submitted left join sitefotos_forms sf on sfs_wo_orignal_form_id = sf_id left join sitefotos_work_orders_fiwo on sf_form_workticket_fiwo_id = swof_id where sfs_id = ?', [formSubmissionID], { useMainPool: true })
        if (fiwoSystem[0].swof_system_id == 8) {
            await command7Services.processFIWOSubmission(formSubmissionID);
        } else if (fiwoSystem[0].swof_system_id == 2) {
            await serviceChannel.fiwoSubmission(formSubmissionID);
        } else if (fiwoSystem[0].swof_system_id == 9) {
            await caseFMSServices.processFIWOSubmission(formSubmissionID);
        } else if (fiwoSystem[0].swof_system_id == smsOneService.providerSystemID) {
            await smsOneService.processFIWOSubmission(formSubmissionID);
        } else if (fiwoSystem[0].swof_system_id == 12) {
            await apiWorkorderService.processFIWOSubmission(formSubmissionID);
        } else if (fiwoSystem[0].swof_system_id == 6) {
            await aspireServices.processFIWOSubmission(formSubmissionID);
        }
    } catch (ex) {
        //Background task therefore no need to throw error?
        console.log(ex);
    }
}

const linkSCPinSite = async (pin, storeId, siteID, vendorID) => {
    try {
        const data = await serviceChannelPinService.linkSite(pin, storeId, siteID, vendorID);
        if (data) {
            return data;
        } else {
            return null;
        }
    } catch (ex) {
        throw ex
    }

}

const addDocs = async (wo, docs, vendorId) => {
    try {
        const MY_NAMESPACE = '9aecf09f-e5c9-4572-bd89-1112660a3d55';
        let key = uuidv5(vendorId.toString(), MY_NAMESPACE);
        for (let doc of docs) {
            const docData = {
                swod_name: doc,
                swod_url: `https://wdocs.sitefotos.com/${key}/${wo}/${doc}`,
                swod_workorder_id: wo,
                swod_vendor_id: vendorId,
            }
            const insertedRecord = await insertObj('sitefotos_work_orders_documents', docData)
        }
    } catch (ex) {
        throw ex
    }
}
const getDocumentUploadURL = async (wo, filename, type, vendorId) => {
    try {
        const MY_NAMESPACE = '9aecf09f-e5c9-4572-bd89-1112660a3d55';
        let key = uuidv5(vendorId.toString(), MY_NAMESPACE);
        return await createPresignedPost(s3Client, {
            Bucket: "sitefotos-workorder-documents",
            Key: `${key}/${wo}/${filename}`,
            Expires: 60 * 60,
            Conditions: [["eq", "$Content-Type", type]],
        });

    } catch (ex) {
        throw ex
    }
}
const modifyWorkOrder = async (wo, vendorId) => {
    try {
        const system = await awaitQuery(`select swou_id from sitefotos_work_orders_users where swou_vendor_id = ? and swou_system_id = ?`, [vendorId, wo.systemID]);
        if ((!system || !system.length) && wo.systemID != 1) {
            throw new Error('Unable to find workorder system.')
        }
        const formTemplate = await awaitQuery(`select sf_form_name, sf_form_data_app, sf_form_emails, sf_form_email_submitter from sitefotos_forms where sf_id=?`, [wo.formTemplate], {useMainPool: true})
        if (!formTemplate || !formTemplate.length) {
            throw new Error("No Form template found for given ID.");
        }
        const siteDetails = await awaitQuery(`select mb_wo_subscriber_id from maptile_building where mb_id=?`, [wo.selectedSite])
        if (!siteDetails || !siteDetails.length) {
            throw new Error("No site details found for given ID.");
        }
        const subscriberId = siteDetails[0]['mb_wo_subscriber_id'] ? siteDetails[0]['mb_wo_subscriber_id'] : 0;
        const status = await calculateStatus(wo.scheduledDate, wo.systemID, wo.status, vendorId, subscriberId)
        const oldData = await awaitQuery(`select swo_form_id, swo_services from sitefotos_work_orders where swo_id=?`, [wo.id]);
        if (!oldData || !oldData.length) {
            throw new Error("No workorder found for given ID.");
        }

        const oldForm = oldData[0]['swo_form_id'];
        let siteArray = []
        siteArray.push(wo.selectedSite.toString())

        // Always call generateForm to handle description and services
        const generatedForm = await generateForm(formTemplate[0], oldData[0].swo_services, wo.woDescription);
        const params = {
            FormID: oldForm,
            FormName: generatedForm,
            FormStatus: status.status == 'OPEN' ? '1' : '3',
            FormData: '',
            AppData: generatedForm,
            Sites: JSON.stringify(siteArray),
            Contacts: JSON.stringify(wo.contacts),
            CheckOut: 1,
            Emails: formTemplate[0]['sf_form_emails'],
            SendAppUser: formTemplate[0]['sf_form_email_submitter'],
            ownform: 1
        }
        await saveForm(vendorId, params);
        let formID = oldForm
        const woData = {
            swo_system_id: wo.systemID,
            swo_status: status.id,
            swo_due_datetime: wo.completionDate ?? null,
            swo_schedule_datetime: wo.scheduledDate ?? null,
            swo_vendor_id: vendorId,
            swo_external_id: wo.woNumber ?? null,
            swo_site_id: wo.selectedSite,
            swo_priority_id: wo.priority ?? null,
            swo_description: wo.woDescription ?? null,
            swo_form_template_id: wo.formTemplate,
            swo_form_id: formID,
            swo_nte: wo.nte ?? null,
            swo_nte_contractor: wo.contractorNte ?? null,
            swo_category_id: wo.categoryID ?? null,
            swo_trade_id: wo.tradeID ?? null,
            swo_close_on_submission: wo.closeOnSubmit ?? 1
        }
        await updateObj('sitefotos_work_orders', woData, ['swo_id'], [wo.id])
        const fData = {
            sf_form_name: formTemplate[0]['sf_form_name'] + ' ' + wo.id,
            sf_form_workticket: 1,
            sf_updated: Math.floor(new Date().getTime() / 1000),
            sf_form_workticket_id: wo.id,
            sf_form_workticket_template_id: wo.formTemplate
        }
        await updateObj('sitefotos_forms', fData, ['sf_id'], [formID]);
        return wo.id;

    }  catch (ex) {
        throw ex
    }
}

const parseExternalSubmission = async (vendorId, system, wo) => {
    if(system == 2) {
        return await serviceChannel.parseExternalSubmission(vendorId, wo);
    } else return null;

}
const forceOpen = async (vendorId, externalId, system) => {
    if(system == 2) {
        return await serviceChannel.forceOpenWorkOrder(vendorId, externalId);
    }
}

const forceClose = async (vendorId, workorderId, system) => {
    if(system == 2) {
        return await serviceChannel.forceCloseWorkOrder(vendorId, workorderId);
    }
}

const saveWorkOrder = async (wo, vendorId) => {
    try {

       
        const system = await awaitQuery(`select swou_id from sitefotos_work_orders_users where swou_vendor_id = ? and swou_system_id = ?`, [vendorId, wo.systemID]);
        if ((!system || !system.length) && wo.systemID != 1) {
            throw new Error('No integration found for the user for this workorder system')
        }

        if(wo.systemID == 3)
            return await emcorServices.createWorkOrder(wo, vendorId);
        if(wo.systemID == 4)
            return await wizardServices.createWorkOrder(wo, vendorId);

        let formTemplate = await awaitQuery(`select sf_form_name, sf_form_data_app, sf_form_emails, sf_form_email_submitter from sitefotos_forms where sf_id=?`, [wo.formTemplate], {useMainPool: true})
        if (!formTemplate || !formTemplate.length) {
            //If no formId or template given, use default template
            const {insertedId, formTemplateNew} = await generateDefaultTemplateForSitefotosWorkorders(vendorId);
            wo.formTemplate = insertedId;
            formTemplate = formTemplateNew;
        }

        const siteDetails = await awaitQuery(`select mb_wo_subscriber_id, mb_nickname from maptile_building where mb_id=?`, [wo.selectedSite])
        if (!siteDetails || !siteDetails.length) {
            throw new Error("No site details found for given ID.");
        }
        const subscriberId = siteDetails[0]['mb_wo_subscriber_id'] ? siteDetails[0]['mb_wo_subscriber_id'] : 0;

        const status = await calculateStatus(wo.scheduledDate, wo.systemID, wo.status, vendorId, subscriberId)
        const bulk = wo.bulk ? wo.bulk : 0;
        let siteArray = []
        siteArray.push(wo.selectedSite.toString());

        // Always call generateForm to handle description and services
        const generatedForm = await generateForm(formTemplate[0], wo.services, wo.woDescription);

        const params = {
            FormName: formTemplate[0]['sf_form_name'],
            FormStatus: status.status == 'OPEN' ? '1' : '3',
            FormData: '',
            AppData: generatedForm,
            Sites: JSON.stringify(siteArray),
            Contacts: JSON.stringify(wo.contacts),
            CheckOut: 1,
            Emails: formTemplate[0]['sf_form_emails'],
            SendAppUser: formTemplate[0]['sf_form_email_submitter'],
            ownform: 1
        }

        


        const formID = await saveForm(vendorId, params);
        const woData = {
            swo_system_id: wo.systemID,
            swo_due_datetime: wo.completionDate ? wo.completionDate : null,
            swo_schedule_datetime: wo.scheduledDate ? wo.scheduledDate : null,
            swo_vendor_id: vendorId,
            swo_external_id: wo.woNumber ? wo.woNumber : null,
            swo_site_id: wo.selectedSite,
            swo_priority_id: wo.priority ? wo.priority : null,
            swo_description: wo.woDescription ? wo.woDescription : null,
            swo_form_template_id: wo.formTemplate,
            swo_form_id: formID,
            swo_nte: wo.nte ? wo.nte : null,
            swo_nte_contractor: wo.contractorNte ? wo.contractorNte : null,
            swo_category_id: wo.categoryID ? wo.categoryID : null,
            swo_trade_id: wo.tradeID ? wo.tradeID : null,
            swo_recurring_schedule_id: wo.scheduleID ? wo.scheduleID : null,
            swo_close_on_submission: wo.closeOnSubmit ?? 1,
            swo_request_datetime: wo.requestedDate ? wo.requestedDate : null,
            swo_due_datetime: wo.dueDate ? wo.dueDate : null,
            swo_status: status.id,
            swo_services: wo.services ? wo.services : null,
            swo_bulk: bulk,
            swo_client_vendor_id: wo.clientVendorId ? wo.clientVendorId : null
        }
        let tradeId = null;
        let tradeName = null;
        if(wo.systemID == 1) {
            let tradeData = await awaitQuery(`SELECT * 
            FROM sitefotos_trades 
            WHERE st_trade COLLATE utf8mb4_0900_ai_ci = (
                SELECT swot_trade 
                FROM sitefotos_work_orders_trades 
                WHERE swot_id = ?
            )`, [wo.tradeID])
            if (tradeData.length > 0){
                tradeId = tradeData[0]['st_id']
                tradeName = tradeData[0]['st_trade']
            }
        }


        const insertedRecord = await insertObj('sitefotos_work_orders', woData)
        //BELOW WE ARE SETTING FORM NAME, IF IT IS TEMPLATE WE ARE USING WO ELSE THE FORM NAME
        const formNameTemp = formTemplate[0]['sf_form_name'] === 'SWO Template' ? 'WO' : formTemplate[0]['sf_form_name'];
        const fData = {
            sf_form_name:  formNameTemp + ' - ' + (tradeName == null ? '':tradeName) + ' - ' + (wo.woNumber ? wo.woNumber + ' - ' : '') + insertedRecord.insertId,
            sf_form_workticket: 1,
            sf_updated: Math.floor(new Date().getTime() / 1000),
            sf_form_workticket_id: insertedRecord.insertId,
            sf_form_workticket_template_id: wo.formTemplate,
            sf_bulk_workorder: bulk,
            sf_trade_id: tradeId
        }
        
        await updateObj('sitefotos_forms', fData, ['sf_id'], [formID]);

        //Email Logic Start
        const validTradeIDs = [1, 2, 3, 31];
        if (validTradeIDs.includes(wo.tradeID) && wo.contacts.length === 1 && wo.contacts[0] > 0) {
            // We will send email if work order is assigned to only 1 contact, be it employee or contractor,
            // and the contact value is not -1, etc.
            woSendEmail(wo.contacts[0], wo.woDescription, siteDetails[0]['mb_nickname'], wo.scheduledDate, wo.woNumber, tradeName, wo.services, wo.priority);
        }

        //Email Logic End
        return insertedRecord.insertId;


    } catch (ex) {
        throw ex
    }
}

const woSendEmail = async (contactId, description, siteName, scheduleDate, externalId, tradeName, serviceIds, priorityId) => {
    let contactDetails = [];
    let priority = [];
    let services = [];
    if (contactId) {
        contactDetails = await awaitSafeQuery(`select CONCAT(sccv.sf_contact_fname, " ", sccv.sf_contact_lname) ContactName, sccv.sf_contact_email as ContactEmail from sitefotos_contacts_company_view sccv where sccv.sf_contact_id =?;`, [contactId]);
    }
    if (priorityId) {
        priority = await awaitSafeQuery(`select * from sitefotos_work_orders_priorities swop where swop.swop_id = ?`, [priorityId]);
    }

    if (serviceIds && serviceIds.length > 0) {
        services = await awaitSafeQuery(` select vs_service_name from vendor_services vs where vs.vs_service_id in (${serviceIds.join(',')})`)
    }
    services = services.map(s => s.vs_service_name);
    sendEmailTemplateSES(
      contactDetails[0].ContactName,
      contactDetails[0].ContactEmail,
      `Sitefotos Workorder Assigned`,
      './static/emails/workorder/sitefotos-workorder-added-email.html',
      {
          'name': contactDetails && contactDetails.length > 0 ? contactDetails[0].ContactName : null,
          'woDescription': description ? description : null,
          'siteName': siteName ? siteName : null,
          'scheduledDate': scheduleDate ? moment.unix(scheduleDate).format('MM/DD/YYYY') : null,
          'externalId': externalId ? externalId : null,
          'trade': tradeName ? tradeName : null,
          'services': services.length > 0 ? services.join(', ') : null,
          'priority': priority.length > 0 ? priority[0].swop_priority : null
      }
    );
}

const generateForm = async (formTemplateData, services = [], description = null) => {
    try {
        // If formTemplateData is not provided or doesn't have the required data, return the existing form data
        if (!formTemplateData || !formTemplateData['sf_form_data_app']) {
            return formTemplateData ? formTemplateData['sf_form_data_app'] : null;
            
        }

        const formJSON = JSON.parse(formTemplateData['sf_form_data_app']);
        let serviceChannelPanel = deepSearchObject(formJSON, 'sitefotos', (_, v) => v === true);
       
        // If no sitefotos panel found, return the original form data
        if (!serviceChannelPanel) {
            return formTemplateData['sf_form_data_app'];
        }

        let questionID = 10000;
        serviceChannelPanel.elements = [];

        if (description != null) {
            const notesField = {
                "type": "textdisplay",
                "text": description
            };
            let notes = JSON.parse(JSON.stringify(notesField));
            notes.name = 'Q' + (++questionID).toString();
            serviceChannelPanel.elements.push(notes);
        }

        if (services && services.length > 0) {
            for(let service of services)
            {
                questionID++;
                const serviceJson = { title: "Service", type: "service", name: "", service: -1, serviceType: "AsTask", isRequired: true };
                let newService = JSON.parse(JSON.stringify(serviceJson));
                newService.service = service.toString();
                newService.name = 'Q' + questionID.toString();
                serviceChannelPanel.elements.push(newService);
            }
        }

        return JSON.stringify(formJSON);
    } catch (ex) {
        console.log(ex)
        // If there's an error, return the original form data if available
        return formTemplateData && formTemplateData['sf_form_data_app'] ? formTemplateData['sf_form_data_app'] : null;
    }
}
const cancelWorkSchedule = async (scheduleId, vendorId) => {
    try {
        let results = await awaitQuery(`select swo_id from sitefotos_work_orders where swo_recurring_schedule_id=? and swo_vendor_id=?`, [scheduleId, vendorId])
        if(results[0])
        {
            for(let result of results)
            {
                await cancelWorkOrder(result['swo_id'], vendorId)
            }
        }
    }catch (ex) {
        throw ex
    }
}

const deleteWorkSchedule = async (scheduleId, vendorId) => {
    try {
        const woObject = {
            swos_active: 0
        }
        await updateObj('sitefotos_work_orders_recurring_schedules', woObject, ['swos_id'], [scheduleId])
        let results = await awaitQuery(`select swo_id from sitefotos_work_orders where swo_recurring_schedule_id=? and swo_vendor_id=?`, [scheduleId, vendorId])
        if(results[0])
        {

            for(let result of results)
            {
                await deleteWorkOrder(result['swo_id'], vendorId)
            }
        }
    }catch (ex) {
        throw ex
    }
}

const deleteWorkOrder = async (woID, vendorId) => {
    try {
        let res = await awaitQuery(`select * from sitefotos_work_orders where swo_id=? and swo_vendor_id=?`, [woID, vendorId])
        if (res[0]) {
            const woObject = {
                swo_active: 0
            }
            await updateObj('sitefotos_work_orders', woObject, ['swo_id'], [woID])
            const foObject = {
                sf_active: '3',
                sf_updated: Math.floor(new Date().getTime() / 1000)
            }
            await updateObj('sitefotos_forms', foObject, ['sf_id'], [res[0]['swo_form_id']])
        }
    }
    catch (ex) {
        throw ex
    }

}
const cancelWorkOrder = async (woID, vendorId) => {
    try {
        let res = await awaitQuery(`select * from sitefotos_work_orders where swo_id=? and swo_vendor_id=?`, [woID, vendorId])
        if (res[0]) {
            const siteDetails = await awaitQuery(`select * from maptile_building where mb_id=?`, [res[0].swo_site_id])
            const subscriberId = siteDetails[0]['mb_wo_subscriber_id'] ? siteDetails[0]['mb_wo_subscriber_id'] : 0;
            const status = 'CANCELLED'
            const systemID = res[0].swo_system_id;

            // Check if status 'CANCELLED' exists
            let statusCheck = await awaitQuery(`SELECT swos_id from sitefotos_work_orders_status where (swos_vendor_id=? or swos_vendor_id=0) and swos_subscriber_id=? and swos_internal_status=? and swos_system_id=?`, [vendorId, subscriberId, status, systemID]);
            let statusId = null;
            // If not, insert it
            if (!statusCheck.length) {
                let obj = {
                    swos_vendor_id: vendorId,
                    swos_subscriber_id: subscriberId,
                    swos_internal_status: status,
                    swos_system_id: systemID,
                    swos_status: 'Cancelled'
                }
                let insertedRecord = await insertObj('sitefotos_work_orders_status', obj);
                statusId = insertedRecord.insertId;
            } else {
                statusId = statusCheck[0]['swos_id'];
            }

            const woObject = {
                swo_status: statusId
            }

            await updateObj('sitefotos_work_orders', woObject, ['swo_id'], [woID])
            const foObject = {
                sf_active: '3',
                sf_updated: Math.floor(new Date().getTime() / 1000)
            }
            await updateObj('sitefotos_forms', foObject, ['sf_id'], [res[0]['swo_form_id']])
        }
    }
    catch (ex) {
        throw ex
    }
}


const getExternalSites = async(vendorID, systemID) => {
  try {

    switch(systemID)
    {
      case '2':
        return await serviceChannel.getSitesInformation(vendorID)
        break;
      default:
        throw new Error("System not found while getting external sites")
    }
  } catch(ex) {
    throw ex
  }
}

const importExternalSites = async(vendorID, systemID) => {
    try {
        switch(systemID)
        {
            case '2':
               serviceChannel.importSites(vendorID)
               break;
            default:
                throw new Error("System not found")
                break;

        }

    } catch(ex) {
        throw ex
    }
}
const addFIWO = async (data, vendorID)=> {
    try {
        const systemID = data.system;
        if (systemID == '1') {
            return await sitefotosWorkorderService.addFIWO(vendorID, data.formTemplate, data.contacts);
        } else if(systemID == '2') {
            return await serviceChannel.enableFIWO(vendorID, data.formTemplate, data.contacts)
        } else if (systemID == '8') {
            return await command7Services.addFIWO(vendorID, data.formTemplate, data.contacts);
        } else if (systemID == '9') {
            return await caseFMSServices.addFIWO(vendorID, data.formTemplate, data.contacts);
        } else if (systemID == '6') {
            return await aspireServices.addFIWO(vendorID, data.trade, data.formTemplate, data.contacts);
        } else if (systemID == smsOneService.providerSystemID) {
            return await smsOneService.addFIWO(vendorID, data.formTemplate, data.contacts);
        } else if (systemID == '12') {
            return await apiWorkorderService.addFIWO(vendorID, data.formTemplate, data.contacts);
        }
    } catch(ex) {
        throw ex
    }
}
const addEAWO = async (data, vendorID)=> {
    try {
        const systemID = data.system;
        if(systemID == '5')
        {
            //Fix it
            fmPilot.addEAWO(vendorID, data.trade, data.formTemplate, data.contacts)
            return true;
        }
        if(systemID == '2')
        {
            return await serviceChannel.enableEAWO(vendorID, data.formTemplate, data.contacts)
        }
        if (systemID == '7') {
            return await corrigoproService.addEAWO(vendorID, data.formTemplate, data.contacts);
        }
        if(systemID == '3') {
            return await emcorServices.addEAWO(vendorID, data.formTemplate, data.contacts);
        }
        if(systemID == '4') {
            return await wizardServices.addEAWO(vendorID, data.formTemplate, data.contacts);
        }
        if (systemID == smsOneService.providerSystemID) {
            return await smsOneService.addEAWO(vendorID, data.formTemplate, data.contacts);
        }
    } catch(ex) {
        throw ex
    }
}
const removeFIWO = async (id,system, vendorID)=> {
    try {
        if (system == '1') {
            return await sitefotosWorkorderService.removeFIWO(vendorID, id)
        }else if(system == '2') {
            return await serviceChannel.removeFIWO(vendorID, id)
        } else if (system == '8') {
            return await command7Services.removeFIWO(vendorID, id);
        } else if (system == '9') {
            return await caseFMSServices.removeFIWO(vendorID, id);
        } else if (system == '6') {
            return await aspireServices.removeFIWO(vendorID, id);
        } else if (system == smsOneService.providerSystemID) {
            return await smsOneService.removeFIWO(vendorID, id);
        } else if (system == '12') {
            return await apiWorkorderService.removeFIWO(vendorID, id);
        }
    } catch(ex) {
        throw ex
    }
}

const removeEAWO = async (id,system, vendorID)=> {
    try {
        if(system == '5')
        {
            return await fmPilot.removeEAWO(vendorID, id)
        }
        if(system == '2')
        {
            return await serviceChannel.removeSIWO(vendorID, id)
        }
        if (system == '7') {
            return await corrigoproService.removeEAWO(vendorID, id);
        }
        if(system == '3')
        {
            return await emcorServices.removeEAWO(vendorID, id)
        }
        if(system == '4')
        {
            return await wizardServices.removeEAWO(vendorID, id)
        }
        if (system == smsOneService.providerSystemID) {
            return await smsOneService.removeEAWO(vendorID, id);
        }
    } catch(ex) {
        throw ex
    }
}

const refreshAllFIWO = async (system, vendorID)=> {
    try {
        if (system == '8') {
            return await command7Services.refreshAllFIWO(vendorID);
        } else if (system == '9') {
            return await caseFMSServices.refreshAllFIWO(vendorID);
        } else if (system == '6') {
            return await aspireServices.refreshAllFIWOForVendor(vendorID);
        }
    } catch(ex) {
        throw ex
    }
}

const refreshAllEAWO = async (system, vendorID) => {
    try {
        if(system == '2')
        {
            return await serviceChannel.backgroundUpdateForSingleUser(vendorID)
        }
        if(system == '5')
        {
            return await fmPilot.backgroundUpdateForSingleUser(vendorID)
        }
        if(system == '7')
        {
            return await corrigoproService.syncAllWorkordersForVendor(vendorID);
        }
        if(system == smsOneService.providerSystemID)
        {
            return await smsOneService.syncAllDataForVendor(vendorID);
        }
    } catch(ex) {
        throw ex
    }
}

const integrate = async(data, vendorID) => {
    try {
        const integrations = await awaitQuery(`select * from sitefotos_work_orders_users where swou_vendor_id=? and swou_system_id=? and swou_active='1'`, [vendorID, data.systemID]);
        let insertedRecord
        switch (data.systemID) {
            case 1:
                if (!integrations || !integrations[0]) {
                    const iData = {
                        'swou_system_id': 1,
                        'swou_vendor_id': vendorID,
                        'swou_production': 1
                    }
                    insertedRecord = await insertObj('sitefotos_work_orders_users', iData)
                    return insertedRecord.insertId;
                }
                else {
                    return integrations[0]['swou_id']
                }
                break;
            case 2:
                insertedRecord = null;
                if (data.pin) {
                    if (!integrations || !integrations[0]) {
                        const iData = {
                            'swou_system_id': 2,
                            'swou_vendor_id': vendorID,
                            'swou_sc_pin_users': 1,
                            'swou_production': data.production,
                        }
                        insertedRecord = (await insertObj('sitefotos_work_orders_users', iData)).insertId
                        enableWorkOrdersPage(vendorID)
                    }
                    else {
                        const uData = {
                            'swou_sc_pin_users': 1
                        }
                        await updateObj('sitefotos_work_orders_users', uData, ['swou_id'], [integrations[0]['swou_id']])
                        insertedRecord =  integrations[0]['swou_id']
                    }
                    return insertedRecord;
                    break;
                }
                if (data.production == null || data.username == null || data.password == null)
                    throw new Error("Integration with Service Channel requires production, username, password")

                let providerID = null;
                if (data.production) {
                    providerID = await serviceChannel.checkCredentials(data.username, data.password, true)

                }
                else {
                    providerID = await serviceChannel.checkCredentials(data.username, data.password, true)
                }


                if (!integrations || !integrations[0]) {

                    const iData = {
                        'swou_system_id': 2,
                        'swou_vendor_id': vendorID,
                        'swou_production': data.production,
                        'swou_username': data.username,
                        'swou_password': data.password,
                        'swou_external_user_id': providerID
                    }
                    insertedRecord = (await insertObj('sitefotos_work_orders_users', iData)).insertId
                    enableWorkOrdersPage(vendorID)
                }
                else {
                    const uData = {
                        'swou_production': data.production,
                        'swou_username': data.username,
                        'swou_password': data.password,
                        'swou_external_user_id': providerID
                    }
                    await updateObj('sitefotos_work_orders_users', uData, ['swou_id'], [integrations[0]['swou_id']])
                    insertedRecord =  integrations[0]['swou_id']
                }
                serviceChannel.importSites(vendorID)
                serviceChannel.populateTrades(vendorID)
                return insertedRecord;
                break;
            case 5:
                if (data.domain == null || data.username == null || data.password == null)
                    throw new Error("Integration with FMPilot requires domain, username, password")


                let test = fmPilot.checkCredentials(data.username, data.password, data.domain)
                if(!test)
                    throw new Error("Invalid credentials")
                insertedRecord = null;
                if (!integrations || !integrations[0]) {

                    const iData = {
                        'swou_system_id': 5,
                        'swou_vendor_id': vendorID,
                        'swou_username': data.username,
                        'swou_password': data.password,
                        'swou_sp_code': data.domain
                    }

                    insertedRecord = (await insertObj('sitefotos_work_orders_users', iData)).insertId
                    enableWorkOrdersPage(vendorID)
                }
                else {
                    const uData = {
                        'swou_username': data.username,
                        'swou_password': data.password,
                        'swou_sp_code': data.domain
                    }
                    await updateObj('sitefotos_work_orders_users', uData, ['swou_id'], [integrations[0]['swou_id']])
                    insertedRecord =  integrations[0]['swou_id']
                }
                fmPilot.populateStatus(vendorID)
                fmPilot.populateTradesServices(vendorID)
                return insertedRecord;
                break;
            case 6:
                if (data.clientID == null || data.clientSecret == null || data.companyName == null) {
                    throw new Error("Integration with Aspire requires clientID, clientSecret and CompanyName")
                }
                insertedRecord = await aspireServices.integrate(vendorID, data, integrations);
                if (insertedRecord) {
                    aspireServices.populateStatus(vendorID);
                    aspireServices.populateTrades(vendorID);
                    enableWorkOrdersPage(vendorID)
                    aspireServices.refreshAllFIWOForVendor(vendorID);
                }
                return insertedRecord;
                break;
            case 7:
                if (data.phone == null) {
                    throw new Error("Integration with Corrigo requires phone number")
                }
                insertedRecord = await corrigoproService.integrate(vendorID, data.phone, integrations);
                if (insertedRecord) {
                    enableWorkOrdersPage(vendorID)
                }
                return insertedRecord;
                break;
            case 8:
                if (!data.phone || !data.pin) {
                    throw new Error("Integration with Command7 requires phone number and company PIN.")
                }
                insertedRecord = await command7Services.integrate(vendorID, data.phone, data.pin, integrations);
                if (insertedRecord) {
                    enableWorkOrdersPage(vendorID)
                }
                return insertedRecord;
                break;
            case 9:
                if (!data.username && !data.token) {
                    throw new Error("Integration with CaseFMS requires valid link sent to mobile/phone.")
                }
                insertedRecord = await caseFMSServices.integrate(vendorID, data.username, data.token, integrations);
                if (insertedRecord) {
                    enableWorkOrdersPage(vendorID)
                }
                return insertedRecord;
                break;
            case 11:
                if (!data.username || !data.password) {
                    throw new Error("Integration with SMS One requires username and password.");
                }
                insertedRecord = await smsOneService.integrate(vendorID, data.username, data.password, integrations);
                if (insertedRecord) {
                    enableWorkOrdersPage(vendorID)
                }
                return insertedRecord;
            case 12:
                if (!integrations || !integrations[0]) {
                    const iData = {
                        'swou_system_id': 12,
                        'swou_vendor_id': vendorID,
                        'swou_production': 1,
                        'swou_fiwo': 1
                    }
                    insertedRecord = await insertObj('sitefotos_work_orders_users', iData)
                    return insertedRecord.insertId;
                } else {
                    return integrations[0]['swou_id']
                }
            default:
                throw new Error("System not found")
                break;
        }


    } catch (ex) {
        throw ex
    }
}

const enableWorkOrdersPage = async (vendorID) => {
    try {
        const page = await awaitQuery(/*SQL*/`select * from sitefotos_user_permissions_pages where supp_id='19'`);
        if(page && page[0]) {
            vendorID = parseInt(vendorID)
            let pageVendors = JSON.parse(page[0]['supp_internal_page_vid']);
            if(pageVendors.indexOf(vendorID) == -1) {
                pageVendors.push(vendorID)
                await awaitQuery(/*SQL*/`update sitefotos_user_permissions_pages set supp_internal_page_vid=? where supp_id='19'`,[JSON.stringify(pageVendors)])
            }
        }

    } catch (ex) {
        console.log(ex || ex.message)
    }
}
const init = async () => {
    try {
        const res1 = await awaitQuery(`select * from sitefotos_work_orders_status limit 1`, [])
        if (!res1 || !res1[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_status (swos_system_id, swos_status, swos_internal_status ) VALUES
            (1,'Scheduled','SCHEDULED'),
            (1,'Open','OPEN'),
            (1,'Paused', 'PAUSED'),
            (1,'In Progress','IN_PROGRESS'),
            (1,'Completed','COMPLETED'),
            (1,'Cancelled','CANCELLED')`, []);
        const res2 = await awaitQuery(`select * from sitefotos_work_orders_trades limit 1`, [])
        if (!res2 || !res2[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_trades (swot_system_id, swot_trade) VALUES
            (1,'Snow'),
            (1,'Janitorial'),
            (1,'Landscape'),
            (1,'HVAC'),
            (1,'Carpet Cleaning')`, [])
        const res3 = await awaitQuery(`select * from sitefotos_work_orders_systems where swos_name='Sitefotos'`, [])
        if (!res3 || !res3[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_systems (swos_name) VALUES
            ('Sitefotos')`, [])

        const res4 = await awaitQuery(`select * from sitefotos_work_orders_priorities limit 1`, [])
        if (!res4 || !res4[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_priorities (swop_system_id, swop_priority) VALUES
            (1,'Urjent'),
            (1,'High'),
            (1,'Medium'),
            (1,'Low')`)


        const res5 = await awaitQuery(`select * from sitefotos_work_orders_systems where swos_name='ServiceChannel'`, [])
        if (!res5 || !res5[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_systems (swos_name,swos_fiwo) VALUES
                ('ServiceChannel',1)`, [])
        const res6 = await awaitQuery(`select * from sitefotos_work_orders_systems where swos_name='EMCOR'`, [])
        if (!res6 || !res6[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_systems (swos_name) VALUES
                    ('EMCOR')`, [])

        /* I did not insert wizard directly into db, find out who did and which system is this */
        const res7 = await awaitQuery(`select * from sitefotos_work_orders_systems where swos_name='WIZARD'`, [])
        if (!res7 || !res7[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_systems (swos_name) VALUES
                    ('WIZARD')`, [])

        const res8 = await awaitQuery(`select * from sitefotos_work_orders_systems where swos_name='FMPilot'`, [])
        if (!res8 || !res8[0])
            await awaitQuery(`INSERT INTO sitefotos_work_orders_systems (swos_name) VALUES
                    ('FMPilot')`, [])


    }
    catch (ex) {
        throw ex
    }
}
const calculateStatus = async (scheduledDate, systemID, status, vendorId, subscriberId) => {
    try {
        const calculatedStatus = moment.unix(scheduledDate).isSameOrBefore(new Date(), 'day') ? 'OPEN' : 'SCHEDULED';
        let result;
        if(systemID == 2)
            result = await awaitQuery(`SELECT swos_id, swos_status, swos_internal_status from sitefotos_work_orders_status where (swos_vendor_id=? or swos_vendor_id=0) and swos_subscriber_id=? and (swos_status=? or swos_internal_status=?) and swos_system_id=?`, [vendorId, subscriberId, status, calculatedStatus, systemID]);
        else
            result = await awaitQuery(`SELECT swos_id, swos_status, swos_internal_status from sitefotos_work_orders_status where (swos_vendor_id=? or swos_vendor_id=0) and (swos_status=? or swos_internal_status=?) and swos_system_id=?`, [vendorId, status, calculatedStatus, systemID]);
        if (!result || !result[0]) {
            throw new Error("Unable to calculate workorder status.");
        }
        let internalStatus = result.find((internalStatus) => internalStatus.swos_status == status) ?? result[0];
        return {
            status: internalStatus['swos_internal_status'],
            id: internalStatus['swos_id']
        }
    }
    catch (ex) {
        throw ex
    }
}

const updateWorkOrders = async () => {
    const workOrders = await awaitQuery(`select * from sitefotos_work_orders left join sitefotos_work_orders_status on swo_status = swos_id where swos_internal_status="SCHEDULED" and swo_system_id in (1,3) and swo_active=1`, [])
    if (workOrders) {
        for (let workOrder of workOrders) {
            if (workOrder['swo_schedule_datetime']) {
                const scheduledDate = workOrder['swo_schedule_datetime'];
               
                const status = moment.unix(scheduledDate).isSameOrBefore(new Date(), 'day') ? 'OPEN' : 'SCHEDULED';
                if (status == 'SCHEDULED')
                    continue;

                const siteDetails = await awaitQuery(`select * from maptile_building where mb_id=?`, [workOrder.swo_site_id])
                if (!siteDetails || siteDetails.length === 0) {
                    console.log(`No site details found for work order ${workOrder.swo_id} with site ID ${workOrder.swo_site_id}`);
                    continue; // Skip this work order and move to the next one
                }
                const subscriberId = siteDetails[0]['mb_wo_subscriber_id'] ? siteDetails[0]['mb_wo_subscriber_id'] : 0;
                const vendorId = workOrder.swo_vendor_id;
                const systemID = workOrder.swo_system_id;
                const woID = workOrder.swo_id;
                const formID = workOrder.swo_form_id;
                const result = await awaitQuery(`SELECT swos_id from sitefotos_work_orders_status where (swos_vendor_id=? or swos_vendor_id=0) and swos_internal_status=? and swos_system_id=?`, [vendorId, status, systemID]);
                if (result && result[0]) {
                    const woObject = {
                        swo_status: result[0]['swos_id']
                    }
                    await updateObj('sitefotos_work_orders', woObject, ['swo_id'], [woID])
                    const foObject = {
                        sf_active: '1',
                        sf_updated: Math.floor(new Date().getTime() / 1000)
                    }
                    await updateObj('sitefotos_forms', foObject, ['sf_id'], [formID])
                }

            }
        }
    }
}

bus.on('CHECKOUT', async(data) => {
    try {
        let {timeStamp, formId, siteId, formSubmissionId, uploaderVid, userVid} = data;
        let formRow = await awaitSafeQuery("select swof_id from sitefotos_work_orders_fiwo where swof_vendor_id= ? and swof_form_template_id = ? and swof_system_id = 1 and swof_active = 1", [userVid, formId], {useMainPool: true});
        if (formRow.length == 1) {
            await sitefotosWorkorderService.processFIWOSubmission(formSubmissionId);
            return
        }
    } catch(ex) {
        console.log("Workorder.services => Check out Event bus exception" + ex.toString());
    }
})

bus.on('CHECKIN', async (data) => {
    try {

        let { timeStamp, formId, siteId, uploaderId, userId,services,uploaderEmail } = data;
        let sql = "SELECT sf_form_workticket, sf_form_provider, sf_form_workticket_id, sf_form_workticket_template_id, sf_form_workticket_sc_pin, sf_form_workticket_fiwo from sitefotos_forms where sf_id = ?";
        let formRow = await awaitSafeQuery(sql, [formId]);
        if (formRow.length == 0) {
            console.info("Workorder.services => No form found for given ID for checkin breadcrumb call.");
            return;
        }
        const tempsystemname = formRow[0]['sf_form_provider'];
        if(tempsystemname == '' || tempsystemname == null) {
            console.info("Workorder.services => No system name found for given form ID for checkin breadcrumb call.");
            return;
        }
        const systemRow = await awaitSafeQuery("SELECT swos_id from sitefotos_work_orders_systems swos WHERE swos_name = ?", [tempsystemname]);
        if(systemRow.length == 0) {
            console.info("Workorder.services => No system found for given system name for checkin breadcrumb call.");
            return;
        }
        const systemID = systemRow[0]['swos_id'];

        //Check in calls to all workorder systems which support checkin on time of actual check in.
        //NOTE: Not sure if we should remove "sf_form_workticket_sc_pin" check? this should be done at node server level? for the system as well?
        // if(formRow[0]['sf_form_workticket']==1 && formRow[0]['sf_form_provider']=='ServiceChannel' && empty(formRow[0]['sf_form_workticket_sc_pin'])) {
        //     let systemID = 2;
        //     let woID = formRow[0]['sf_form_workticket_id'];

        //     await checkinBreadcrumb(systemID, formId, woID, timeStamp);
        // } else if (formRow[0]['sf_form_workticket'] == 1 && formRow[0]['sf_form_provider']!='ServiceChannel') {
        //     let woID = formRow[0]['sf_form_workticket_id'];
        //     await checkinBreadcrumb(systemID, formId, woID, timeStamp);
        // } else
        if (formRow[0]['sf_form_workticket_fiwo'] == 1) {
            await checkinBreadcrumbForFIWO(systemID, formId, timeStamp, siteId, services, uploaderId, userId,uploaderEmail);
        }
    } catch(ex) {
        console.log("Workorder.services => Check in Event bus exception" + ex.toString());
    }
});

const checkinBreadcrumb = async(systemID, formID, woID, timestamp) => {
    try {
        if(systemID == 2){
            serviceChannel.checkinBreadcrumb(formID, woID, timestamp)
        }
        if (systemID == 11) {
            smsOneService.checkinBreadcrumb(formID, woID, timestamp);
        }
    }
    catch (ex) {
        throw ex
    }
}

const checkinBreadcrumbForFIWO = async(systemID, formID, timestamp,siteId, services, uploaderId, userId,uploaderEmail) => {
    try {
        switch (systemID) {
            case 2:
              /* await serviceChannel.fiwoCheckIn(formID, timestamp, siteId, services, uploaderId, userId,uploaderEmail); */
                break;
            case 9:
                await caseFMSServices.checkinBreadcrumb(formID, timestamp);
                break;
            case 11:
                await smsOneService.checkinBreadcrumbForFIWO(formID, timestamp, siteId, services, uploaderId, userId, uploaderEmail);
                break;
            default:
                break;
        }
    } catch (ex) {
        console.log(ex);
    }
}

bus.on('CANCELCHECKIN', async (data) => {
    try {
        let { timeStamp, formId, siteId, uploaderVid, userVid } = data;
        let sql = "SELECT sf_form_workticket, sf_form_provider, sf_form_workticket_id, sf_form_workticket_template_id, sf_form_workticket_sc_pin, sf_form_workticket_fiwo from sitefotos_forms where sf_id = ?";
        let formRow = await awaitSafeQuery(sql, [formId]);

        if (formRow.length == 0) {
            console.log("Workorder.services => No form found for given ID for cancel checkin breadcrumb call.");
            return
        }
        let tempsystemname = formRow[0]['sf_form_provider'];
        if(tempsystemname == '' || tempsystemname == null) {
            console.info("Workorder.services => No system name found for given form ID for cancel checkin breadcrumb call.");
            return;
        }
        let systemRow = await awaitSafeQuery("SELECT swos_id from sitefotos_work_orders_systems swos WHERE swos_name = ?", [tempsystemname]);
        let systemID = systemRow[0]['swos_id'];

        if (formRow[0]['sf_form_workticket']==1) {
            let woID = formRow[0]['sf_form_workticket_id'];
            await cancelCheckinBreadcrumb(systemID, formId, woID, timeStamp);
        } else if (formRow[0]['sf_form_workticket_fiwo'] == 1) {
            await cancelCheckInForFIWO(systemID, formId, siteId, uploaderVid, userVid, timeStamp);
        }
    } catch(ex) {
        console.log("Workorder.services => Cancel Check in Event bus exception" + ex.toString());
    }
});

const cancelCheckinBreadcrumb = async(systemID, formID, woID, timestamp) => {
    try {
        if(systemID == 11){
            smsOneService.cancelCheckInBreadcrumb(formID, woID, timestamp)
        }
    }
    catch (ex) {
        throw ex
    }
}

const cancelCheckInForFIWO = async(systemID, formID, siteID, uploaderVid, userVid, timeStamp) => {
    try {
        if (systemID == 9) {
            await caseFMSServices.cancelCheckInBreadcrumb(formID, timeStamp);
        } else if (systemID == 11) {
            await smsOneService.cancelCheckInBreadcrumbFIWO(formID, siteID, uploaderVid, userVid, timeStamp);
        }
    } catch (ex) {
        console.log(ex);
    }
}

const submit = async (systemID, submissionID, woID) =>{
    try {
        if(systemID == 5){
            fmPilot.submit(submissionID, woID)
        }
        if(systemID == 2) {
            serviceChannel.submit(submissionID,woID)
        }
        if (systemID == 7) {
            corrigoproService.submit(submissionID, woID);
        }
        if (systemID == 11) {
            smsOneService.submit(submissionID, woID);
        }
        if (systemID == 3) {
            emcorServices.submit(woID);
        }
    } catch (ex) {
        throw ex
    }
}
const saveBulkWorkOrders = async (data, vendorId) => {
    try {
        let  {selectedSite, system, trade, formTemplate, contacts, closeOnSubmit, newTemplate, newTemplateName, services, woDescription} = data;
        if(!system) system = data.systemID;
        if(!trade) trade = data.tradeID;
        let form = await awaitQuery("SELECT * FROM sitefotos_forms WHERE sf_id = ?", [formTemplate]);
        if (!form || !form.length) {
            const resp = await generateDefaultTemplateForSitefotosWorkorders(vendorId);
            form = resp.formTemplateNew;
        }
        let tradeData = await awaitQuery(`SELECT * 
            FROM sitefotos_trades 
            WHERE st_trade COLLATE utf8mb4_0900_ai_ci = (
                SELECT swot_trade 
                FROM sitefotos_work_orders_trades 
                WHERE swot_id = ?
            )`, [trade])
        let tradeId = null;
        if (tradeData.length > 0)
            tradeId = tradeData[0]['st_id']
        if(newTemplate) {
            //create a new template and save the form and get its id
            const params = {
                FormName: newTemplateName,
                FormStatus: '1',
                FormData: '',
                AppData: form[0]['sf_form_data_app'],
                Sites: '[]',
                Contacts: '[]',
                CheckOut: 1,
                Emails: form[0]['sf_form_emails'],
                SendAppUser: form[0]['sf_form_email_submitter'],
                ownform: 1,
                tradeID: tradeId,
                editable: form[0]['sf_form_editable'] == 1 ? true : false,
            }
            formTemplate = await saveForm(vendorId, params);

        }
        for (const siteId of selectedSite) {
            let workOrder = {
                systemID: system,
                tradeID: trade,
                scheduledDate: moment().unix(),
                contacts: contacts,
                selectedSite: siteId,
                formTemplate: formTemplate,
                closeOnSubmit: closeOnSubmit,
                services: services,
                bulk: 1,
                woDescription:  woDescription,
                nte: data.nte ? data.nte : 0,
                contractorNte: data.contractorNte ? data.contractorNte : 0
            }
            await saveWorkOrder(workOrder, vendorId)
        }




    } catch (ex) {
        console.log(ex)
    }

}
const saveSchedule = async (schedule, vendorId) => {
    try {
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const weeks = ["First", "Second", "Third", "Fourth", "Last"];
      const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

      const startDate = moment.unix(schedule.startDate);
      const endDate = moment.unix(schedule.endDate);
      const selectedWeekDays = schedule.weekDays || [0];

      for (const siteId of schedule.selectedSite) {
        const scheduleObj = {
          swos_vendor_id: vendorId,
          swos_system_id: schedule.system,
          swos_trade_id: schedule.trade,
          swos_weeks: schedule.weeks.map(x => weeks[x]).join(),
          swos_months: schedule.months.map(x => months[x]).join(),
          swos_weekdays: selectedWeekDays.map(x => weekDays[x]).join(),
          swos_site_id: siteId,
          swos_form_template_id: schedule.formTemplate,
          swos_start_date: schedule.startDate,
          swos_end_date: schedule.endDate
        };

        const insertedSchedule = await insertObj(`sitefotos_work_orders_recurring_schedules`, scheduleObj);

        let workOrder = {
          systemID: schedule.system,
          tradeID: schedule.trade,
          scheduledDate: null,
          contacts: schedule.contacts,
          selectedSite: siteId,
          formTemplate: schedule.formTemplate,
          scheduleID: insertedSchedule.insertId,
          closeOnSubmit: 1,
        };

        for (let currentDate = moment(startDate); currentDate.diff(endDate, 'days') <= 0; currentDate.add(1, 'days')) {
            const month = currentDate.clone().month();
            const weekDayIndex = currentDate.day();

            if (schedule.months.includes(month) && selectedWeekDays.includes(weekDayIndex)) {
              workOrder.scheduledDate = currentDate.unix();
              await saveWorkOrder(workOrder, vendorId);
            }
          }
      }
    } catch (ex) {
      console.error('Error in saveSchedule:', ex);
      throw new Error('Failed to save schedule');
    }
  };
/*const saveSchedule = async (schedule, vendorId) => {
    try {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        const weeks = ["First", "Second", "Third", "Fourth", "Last"]
        const startDate = moment.unix(schedule.startDate)
        const endDate = moment.unix(schedule.endDate)
        const scheduleObj = {
            swos_vendor_id: vendorId,
            swos_system_id: schedule.system,
            swos_trade_id: schedule.trade,
            swos_weeks: schedule.weeks.map(x => weeks[x]).join(),
            swos_months: schedule.months.map(x => months[x]).join(),
            swos_weekstart: 'Sun',
            swos_site_id: schedule.selectedSite,
            swos_form_template_id: schedule.formTemplate,
            swos_start_date: schedule.startDate,
            swos_end_date: schedule.endDate
        }
        const insertedSchedule = await insertObj(`sitefotos_work_orders_recurring_schedules`, scheduleObj)


        let workOrder = {
            systemID: schedule.system,
            tradeID: schedule.trade,
            scheduledDate: null,
            contacts: schedule.contacts,
            selectedSite: schedule.selectedSite,
            formTemplate: schedule.formTemplate,
            tradeID: schedule.trade,
            scheduleID: insertedSchedule.insertId,
            closeOnSubmit: 1,
        }
        for (let currentDate = moment(startDate); currentDate.diff(endDate, 'days') <= 0; currentDate.add(1, 'days')) {

            const firstDayOfMonth = currentDate.clone().startOf('month');
            const firstDayOfWeek = firstDayOfMonth.clone().startOf('week');
            const offset = firstDayOfMonth.diff(firstDayOfWeek, 'days');

            const weekofMonth = Math.ceil((currentDate.date() + offset) / 7) - 1
            const endofMonth = currentDate.clone().endOf("month")
            const weeksInMonth = Math.ceil((endofMonth.date() + offset) / 7) - 1
            const dateofMonth = currentDate.clone().date();
            const day = currentDate.clone().day();
            const month = currentDate.clone().month();
            workOrder.scheduledDate = currentDate.unix()

            if (schedule.weeks.includes(weekofMonth) && dateofMonth == 1 && weekofMonth == 0 && schedule.months.includes(month))
                await saveWorkOrder(workOrder, vendorId)

            if (schedule.weeks.includes(weekofMonth) && weekofMonth == 1 && schedule.months.includes(month) && day == 0)
                await saveWorkOrder(workOrder, vendorId)

            if (schedule.weeks.includes(weekofMonth) && weekofMonth == 2 && schedule.months.includes(month) && day == 0)
                await saveWorkOrder(workOrder, vendorId)

            if (schedule.weeks.includes(weekofMonth) && weekofMonth == 3 && schedule.months.includes(month) && day == 0)
                await saveWorkOrder(workOrder, vendorId)

            if (schedule.weeks.includes(weekofMonth) && weekofMonth == weeksInMonth && schedule.months.includes(month) && day == 0)
                await saveWorkOrder(workOrder, vendorId)



        }
    } catch (ex) {
        throw ex
    }

} */

const refreshAllWorkorders = async (vendorId) => {
    try {
        await fmPilot.backgroundUpdateForSingleUser(vendorId);
    } catch (e) {
        console.log("Error updating fmpilot")
        console.log(e || e.message)
    }
    try {
        await serviceChannel.backgroundUpdateForSingleUser(vendorId);
    }   catch (e) {
        console.log("Error updating service channel")
        console.log(e || e.message)
    }
    refreshCorrigoWorkorders(vendorId);
    refreshSMSoneWorkorders(vendorId);
    refreshAspireFIWO(vendorId);
}

const refreshCorrigoWorkorders = async (vendorID) => {
    try {
        await corrigoproService.syncAllWorkordersForVendor(vendorID)
    }   catch (e) {
        console.log("Corrigopro => Error refreshing Corrigopro workorders." + e || e.message);
    }
}

const refreshSMSoneWorkorders = async (vendorID) => {
    try {
        await smsOneService.syncAllDataForVendor(vendorID);
    }   catch (e) {
        console.log("SMSOne => Error refreshing smsone workorders." + e || e.message);
    }
}


const refreshAspireFIWO = async (vendorID) => {
    try {
        await aspireServices.refreshAllFIWOForVendor(vendorID);
    }   catch (e) {
        console.log("SMSOne => Error refreshing smsone workorders." + e || e.message);
    }
}

const generateDefaultTemplateForSitefotosWorkorders = async (vendorId) =>{
    let sitefotosWoTemplateForThisVendor =
      await awaitQuery(`select * from sitefotos_forms where sf_vendor_id=? and sf_default_template=1`, [vendorId]);
    if (!sitefotosWoTemplateForThisVendor || !sitefotosWoTemplateForThisVendor.length){
        //Template does not exist, lets create it in the user's account.
        const templateJSON = { "pages": [{ "name": "Page", "title": "SWO Template", "elements": [{ "title": "Before Photo(s)", "type": "file", "name": "Q1", "isRequired": false }, { "title": "Tasks", "type": "panel", "sitefotos": true, "name": "Q0", "repeatable": false, "isRequired": false, "elements": [] }, { "title": "After Photo(s)", "type": "file", "name": "Q1", "isRequired": false }] }] };
        const params = {
            FormName: 'SWO Template',
            FormStatus: '1',
            FormData: '',
            AppData: JSON.stringify(templateJSON),
            Sites: '[]',
            Contacts: '[]',
            CheckOut: 1,
            Emails: '',
            SendAppUser: "1",
            ownform: 1
        };
        const savedFormId = await saveForm(vendorId, params);
        await updateObj('sitefotos_forms', {sf_default_template: 1}, ['sf_id'], [savedFormId]);
        let sitefotosWoTemplateForAllUsers = await awaitQuery(`select * from sitefotos_forms where sf_vendor_id=? and sf_id=? and sf_default_template=1`, [vendorId, savedFormId], {useMainPool: true});
        return {insertedId: savedFormId, formTemplateNew: sitefotosWoTemplateForAllUsers};
    }
    else {
        return {insertedId: sitefotosWoTemplateForThisVendor[0].sf_id, formTemplateNew: sitefotosWoTemplateForThisVendor};
    }
}


const setInternalWorkOrderStatus = async (vendorId, workorderId, targetStatus) => {
  try {

    let res = await awaitQuery('select swo_id, swo_system_id, swo_form_id from sitefotos_work_orders where swo_id=? and swo_vendor_id=?', [workorderId, vendorId]);

   
    if (!res || res.length === 0) {
      throw new Error('Workorder not found.');
    }

    const workorder = res[0];

    
    if (workorder.swo_system_id !== 1) {
      throw new Error('This operation is only allowed for internal work orders (system_id 1).');
    }

   
    let targetStatusId;
    if (targetStatus === 'Completed') {
      targetStatusId = 5; 
    } else if (targetStatus === 'Cancelled') {
      targetStatusId = 6; 
    } else {
      
      throw new Error('Invalid target status provided.');
    }

   
    const woObject = { swo_status: targetStatusId };
    await updateObj('sitefotos_work_orders', woObject, ['swo_id'], [workorderId]);

    
    const formId = workorder.swo_form_id;
    if (formId) {
      const foObject = {
        sf_active: '3', 
        sf_updated: Math.floor(new Date().getTime() / 1000)
      };
      await updateObj('sitefotos_forms', foObject, ['sf_id'], [formId]);
    }

    return true; 

  } catch (ex) {
    console.error('Error in setInternalWorkOrderStatus:', ex);
    
    throw ex;
  }
};

const propagateTradeMapping = async (swot_id, st_id, vendorId) => {
    console.log(`Starting trade mapping propagation for swot_id: ${swot_id}, st_id: ${st_id}, vendorId: ${vendorId}`);
    try {
        
        const externalTradeResult = await awaitQuery('SELECT swot_trade FROM sitefotos_work_orders_trades WHERE swot_id = ?', [swot_id]);
        if (!externalTradeResult || externalTradeResult.length === 0) {
            console.error(`Propagate Error: External trade not found for swot_id ${swot_id}`);
            return; 
        }
        const externalTradeName = externalTradeResult[0].swot_trade;
        console.log(`  External trade name: ${externalTradeName}`);

      
        try {
            console.log(`  Updating sitefotos_forms...`);
            const formsToUpdate = await awaitQuery(
                `SELECT sf_id FROM sitefotos_forms
                 WHERE sf_form_workticket_id IN (
                     SELECT swo_id FROM sitefotos_work_orders
                     WHERE swo_trade_id = ? AND swo_vendor_id = ?
                 )`,
                [swot_id, vendorId]
            );
            const formIds = formsToUpdate.map(f => f.sf_id);
            if (formIds.length > 0) {
                const updateFormsResult = await updateObj(
                    'sitefotos_forms',
                    { sf_trade_id: st_id },
                    ['sf_id'],
                    [formIds]
                );
                console.log(`    Updated ${updateFormsResult.affectedRows || updateFormsResult.affectedRows === 0 ? updateFormsResult.affectedRows : (updateFormsResult.changedRows || 0)} forms.`);

                
                try {
                    console.log(`  Updating sitefotos_forms_submitted...`);
                    const submissionsToUpdate = await awaitQuery(
                        `SELECT sfs_id FROM sitefotos_forms_submitted WHERE sfs_wo_orignal_form_id IN (?)`,
                        [formIds]
                    );
                    const submissionIds = submissionsToUpdate.map(s => s.sfs_id);
                    if (submissionIds.length > 0) {
                        const updateSubmissionsResult = await updateObj(
                            'sitefotos_forms_submitted',
                            { sfs_trade_id: st_id },
                            ['sfs_id'],
                            [submissionIds]
                        );
                        console.log(`    Updated ${updateSubmissionsResult.affectedRows || updateSubmissionsResult.affectedRows === 0 ? updateSubmissionsResult.affectedRows : (updateSubmissionsResult.changedRows || 0)} submissions.`);
                    } else {
                        console.log(`    No submissions found for the updated forms.`);
                    }
                } catch (subEx) {
                    console.error(`Propagate Error: Failed to update sitefotos_forms_submitted for swot_id ${swot_id}:`, subEx);
                }

            } else {
                console.log(`    No forms found linked to work orders for this external trade.`);
            }
        } catch (formEx) {
            console.error(`Propagate Error: Failed to update sitefotos_forms for swot_id ${swot_id}:`, formEx);
        }


        
        try {
            console.log(`  Updating vendor_services...`);
            
            const servicesToUpdate = await awaitQuery(
                `SELECT vs_service_id FROM vendor_services
                 WHERE vs_vendor_id = ?
                   AND vs_service_category = ?
                   AND vs_provider = 'ServiceChannel'
                   AND vs_trade_id IS NULL`,
                [vendorId, externalTradeName]
            );
            const serviceIds = servicesToUpdate.map(s => s.vs_service_id);
            if (serviceIds.length > 0) {
                const updateServicesResult = await updateObj(
                    'vendor_services',
                    { vs_trade_id: st_id },
                    ['vs_service_id'],
                    [serviceIds]
                );
                console.log(`    Updated ${updateServicesResult.affectedRows || updateServicesResult.affectedRows === 0 ? updateServicesResult.affectedRows : (updateServicesResult.changedRows || 0)} vendor services.`);
            } else {
                 console.log(`    No vendor services found matching the external trade name category.`);
            }
        } catch (serviceEx) {
            console.error(`Propagate Error: Failed to update vendor_services for swot_id ${swot_id}:`, serviceEx);
        }

        console.log(`Finished trade mapping propagation for swot_id: ${swot_id}`);

    } catch (error) {
        console.error(`Propagate Error: Unhandled exception during propagation for swot_id ${swot_id}:`, error);
    }
};


const mapTrade = async (swot_id, st_id, vendorId) => {
    try {
        console.log(`Mapping trade swot_id: ${swot_id} to st_id: ${st_id} for vendorId: ${vendorId}`);
        const updateResult = await updateObj(
            'sitefotos_work_orders_trades',
            { swot_internal_trade_id: st_id },
            ['swot_id', 'swot_vendor_id'],
            [swot_id, vendorId]
        );
        console.log(updateResult)
        if (updateResult.affectedRows === 0) {
             console.warn(`Trade mapping update affected 0 rows for swot_id: ${swot_id}, vendorId: ${vendorId}. Trade might not exist or already mapped.`);
            
        } else {
             console.log(`Successfully updated mapping in sitefotos_work_orders_trades for swot_id: ${swot_id}.`);
             
             propagateTradeMapping(swot_id, st_id, vendorId);
        }

        return true; 

    } catch (ex) {
        console.error(`Error mapping trade swot_id ${swot_id} to st_id ${st_id}:`, ex);
        throw new Error(`Failed to map trade: ${ex.message}`);
    }
};

const getInternalTrades = async () => {
    try {
        const trades = await awaitQuery(`SELECT st_id, st_trade FROM sitefotos_trades ORDER BY st_trade;`);
        return trades;
    } catch (ex) {
        console.error('Error fetching internal trades:', ex);
        throw new Error('Failed to fetch internal trades');
    }
};

const getExternalTradesForMapping = async (vendorId, systemId) => {
    try {
        
        const trades = await awaitQuery(
            `SELECT swot_id, swot_trade, swot_internal_trade_id
             FROM sitefotos_work_orders_trades
             WHERE swot_vendor_id = ? AND swot_system_id = ?
             ORDER BY swot_trade;`,
            [vendorId, systemId]
        );
        return trades;
    } catch (ex) {
        console.error(`Error fetching external trades for mapping (vendor: ${vendorId}, system: ${systemId}):`, ex);
        throw new Error('Failed to fetch external trades for mapping');
    }
};

const closeServiceChannelWorkorder = async (workorderId, vendorId) => {
    try {
        const workorderResults = await awaitQuery(
            `SELECT swo_id, swo_system_id, swo_external_id, swo_form_id, swo_vendor_id
             FROM sitefotos_work_orders
             WHERE swo_id = ?`,
            [workorderId]
        );

        if (!workorderResults || workorderResults.length === 0) {
            throw new Error('Workorder not found.');
        }

        const wo = workorderResults[0];

        if (wo.swo_vendor_id !== vendorId) {
            throw new Error('Workorder does not belong to the authenticated vendor.');
        }

        if (wo.swo_system_id !== 2) {
            throw new Error('Workorder is not a Service Channel workorder.');
        }

        if (!wo.swo_external_id) {
            throw new Error('Service Channel External ID is missing for this workorder.');
        }

        const scUpdateSuccess = await serviceChannel.updateStatus(vendorId, wo.swo_external_id, 'COMPLETED', 'CLOSED');

        if (!scUpdateSuccess) {
            throw new Error('Failed to update workorder status in Service Channel.');
        }

        await updateObj('sitefotos_work_orders', { swo_status: 5 }, ['swo_id'], [workorderId]);

        if (wo.swo_form_id) {
            await updateObj('sitefotos_forms', { sf_active: '3', sf_updated: Math.floor(new Date().getTime() / 1000) }, ['sf_id'], [wo.swo_form_id]);
        }

        return { success: true, message: 'Service Channel workorder closed successfully.' };

    } catch (ex) {
        console.error(`Error in closeServiceChannelWorkorder for WO ID ${workorderId}, Vendor ID ${vendorId}:`, ex);
        throw ex; 
    }
};

module.exports = {
    saveWorkOrder,
    saveSchedule,
    cancelWorkOrder,
    init,
    updateWorkOrders,
    getDocumentUploadURL,
    modifyWorkOrder,
    addDocs,
    cancelWorkSchedule,
    deleteWorkOrder,
    deleteWorkSchedule,
    integrate,
    getExternalSites,
    importExternalSites,
    fiwoSubmit,
    addFIWO,
    addEAWO,
    removeFIWO,
    refreshAllFIWO,
    removeEAWO,
    refreshAllEAWO,
    submit,
    refreshAllWorkorders,
    linkSCPinSite,
    checkinBreadcrumb,
    cancelCheckinBreadcrumb,
    parseExternalSubmission,
    forceOpen,
    forceClose,
    saveBulkWorkOrders,
    calculateStatus,
    changeAssignment,
    setInternalWorkOrderStatus, 
    getInternalTrades, 
    mapTrade, 
    propagateTradeMapping,
    getExternalTradesForMapping,
    closeServiceChannelWorkorder
}
